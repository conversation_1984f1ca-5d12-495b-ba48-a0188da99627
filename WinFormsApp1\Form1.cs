using Microsoft.EntityFrameworkCore;
using Models.HandleData;

namespace WinFormsApp1
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
            SetupForm();
            LoadRememberedCredentials();
        }

        private void SetupForm()
        {
            this.Text = "Đăng nhập hệ thống";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;

            // Thiết lập mật khẩu ẩn
            matkhau.PasswordChar = '*';

            // <PERSON><PERSON> sự kiện cho nút đăng nhập
            button1.Click += Button1_Click;

            // <PERSON><PERSON> sự kiện cho link quên mật khẩu
            // Cho phép nhấn Enter để đăng nhập
            this.KeyPreview = true;
            this.KeyDown += Form1_KeyDown;
        }

        private void Form1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                Button1_Click(sender, e);
            }
        }

        private async void Button1_Click(object sender, EventArgs e)
        {
            string username = taikhoan.Text.Trim();
            string password = matkhau.Text.Trim();

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                MessageBox.Show("Vui lòng nhập đầy đủ tên đăng nhập và mật khẩu!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Hiển thị loading
            button1.Enabled = false;
            button1.Text = "Đang đăng nhập...";

            try
            {
                using (var context = new DAContext())
                {
                    // Tìm tài khoản trong database
                    var user = await context.TaiKhoan
                        .Include(t => t.CanBo)
                        .FirstOrDefaultAsync(t => t.TenDangNhap == username && t.MatKhau == password);

                    if (user != null)
                    {
                        // Lưu thông tin đăng nhập nếu được chọn Remember Me
                        SaveCredentialsIfRemembered(username);

                        // Đăng nhập thành công
                        MessageBox.Show($"Đăng nhập thành công! Xin chào {user.CanBo?.HoTen ?? user.TenDangNhap}", "Thành công", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // Mở form chính và ẩn form đăng nhập
                        MainFormNew mainForm = new MainFormNew(user);

                        // Đăng ký sự kiện khi MainForm đóng để hiển thị lại Form1
                        mainForm.FormClosed += (s, args) =>
                        {
                            this.Show();
                            this.WindowState = FormWindowState.Normal;
                            this.BringToFront();
                            // Reset form đăng nhập
                            matkhau.Clear();
                            if (!chkRememberMe.Checked)
                            {
                                taikhoan.Clear();
                            }
                            taikhoan.Focus();
                        };

                        mainForm.Show();
                        this.Hide();
                    }
                    else
                    {
                        // Đăng nhập thất bại
                        MessageBox.Show("Tên đăng nhập hoặc mật khẩu không đúng!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        matkhau.Clear();
                        taikhoan.Focus();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi kết nối cơ sở dữ liệu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Khôi phục trạng thái nút
                button1.Enabled = true;
                button1.Text = "Đăng nhập";
            }
        }

        private void button1_Click_1(object sender, EventArgs e)
        {

        }

        private void LoadRememberedCredentials()
        {
            try
            {
                // Kiểm tra xem có thông tin đăng nhập được lưu không
                if (Properties.Settings.Default.RememberMe)
                {
                    taikhoan.Text = Properties.Settings.Default.SavedUsername ?? string.Empty;
                    chkRememberMe.Checked = true;

                    // Focus vào ô mật khẩu nếu đã có tên đăng nhập
                    if (!string.IsNullOrEmpty(taikhoan.Text))
                    {
                        matkhau.Focus();
                    }
                }
                else
                {
                    taikhoan.Focus();
                }
            }
            catch (Exception ex)
            {
                // Nếu có lỗi khi đọc settings, bỏ qua và tiếp tục
                System.Diagnostics.Debug.WriteLine($"Lỗi khi tải thông tin đăng nhập: {ex.Message}");
                taikhoan.Focus();
            }
        }

        private void SaveCredentialsIfRemembered(string username)
        {
            try
            {
                if (chkRememberMe.Checked)
                {
                    Properties.Settings.Default.RememberMe = true;
                    Properties.Settings.Default.SavedUsername = username;
                }
                else
                {
                    Properties.Settings.Default.RememberMe = false;
                    Properties.Settings.Default.SavedUsername = string.Empty;
                }
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                // Nếu có lỗi khi lưu settings, bỏ qua
                System.Diagnostics.Debug.WriteLine($"Lỗi khi lưu thông tin đăng nhập: {ex.Message}");
            }
        }

        private void LinkForgotPassword_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            MessageBox.Show("Test",
                           "Quên mật khẩu",
                           MessageBoxButtons.OK,
                           MessageBoxIcon.Information);
        }
    }
}
